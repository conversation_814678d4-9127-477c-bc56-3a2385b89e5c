import 'package:flutter_test/flutter_test.dart';
import 'package:bus/data/models/notification_settings_models/notification_settings_model.dart';
import 'package:bus/data/repo/notification_settings_repo.dart';

void main() {
  group('Notification Settings Tests', () {
    test('NotificationSettingsModel should parse JSON correctly', () {
      // Test data based on the expected API response
      final jsonData = {
        "status": {
          "status": 1,
          "messages": "Data updated successfully"
        },
        "data": {
          "id": 2,
          "name": "<EMAIL>",
          "email": "<EMAIL>",
          "phone": "01127453362",
          "email_verified_at": "1",
          "address": "Mansoura",
          "city_name": ".<EMAIL>",
          "status": 1,
          "logo": "default.png",
          "created_at": "2025-04-30T07:51:53.000000Z",
          "updated_at": "2025-05-20T12:16:53.000000Z",
          "typeAuth": "school",
          "latitude": "90",
          "longitude": "90",
          "trip_start_end_notification_status": "1",
          "student_absence_notification_status": "0",
          "student_address_notification_status": "0",
          "logo_path": "https://test.busatyapp.com/uploads/schools_logo/default.png",
          "subscription_status": false,
          "identity_preview": true
        }
      };

      final model = NotificationSettingsModel.fromJson(jsonData);

      expect(model.status, true);
      expect(model.message, "Data updated successfully");
      expect(model.data?.id, 2);
      expect(model.data?.tripStartEndNotificationStatus, "1");
      expect(model.data?.studentAbsenceNotificationStatus, "0");
      expect(model.data?.studentAddressNotificationStatus, "0");
    });

    test('NotificationSettingsRepo should be instantiable', () {
      final repo = NotificationSettingsRepo();
      expect(repo, isNotNull);
    });
  });
}
