import 'package:bus/bloc/notification_settings_cubit/notification_settings_cubit.dart';
import 'package:bus/bloc/notification_settings_cubit/notification_settings_states.dart';
import 'package:bus/config/theme_colors.dart';
import 'package:bus/helper/cache_helper.dart';
import 'package:bus/translations/local_keys.g.dart';
import 'package:bus/views/custom_widgets/custom_text.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class NotificationSettingsBottomSheet extends StatefulWidget {
  const NotificationSettingsBottomSheet({super.key});

  @override
  State<NotificationSettingsBottomSheet> createState() =>
      _NotificationSettingsBottomSheetState();
}

class _NotificationSettingsBottomSheetState
    extends State<NotificationSettingsBottomSheet> {
  // Default values for the switches
  bool _tripNotificationsEnabled = true;
  bool _addressChangeNotificationsEnabled = true;
  bool _absenceNotificationsEnabled = true;

  @override
  void initState() {
    super.initState();
    _loadNotificationSettings();
  }

  // Load saved notification settings from SharedPreferences
  void _loadNotificationSettings() {
    setState(() {
      _tripNotificationsEnabled =
          CacheHelper.getBool('trip_notifications_enabled') ?? true;
      _addressChangeNotificationsEnabled =
          CacheHelper.getBool('address_change_notifications_enabled') ?? true;
      _absenceNotificationsEnabled =
          CacheHelper.getBool('absence_notifications_enabled') ?? true;
    });
  }

  // Save notification settings to SharedPreferences and API
  void _saveNotificationSetting(String key, bool value) {
    CacheHelper.putBool(key, value);
    _updateNotificationSettingsAPI();
  }

  // Update notification settings via API
  void _updateNotificationSettingsAPI() {
    NotificationSettingsCubit.get(context).updateNotificationSettings(
      tripStartEndNotificationStatus: _tripNotificationsEnabled,
      studentAbsenceNotificationStatus: _absenceNotificationsEnabled,
      studentAddressNotificationStatus: _addressChangeNotificationsEnabled,
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => NotificationSettingsCubit(),
      child:
          BlocConsumer<NotificationSettingsCubit, NotificationSettingsStates>(
        listener: (context, state) {
          if (state is NotificationSettingsSuccessStates) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                backgroundColor: TColor.greenSuccess,
                content: CustomText(
                  text: AppStrings.successfullyDone.tr(),
                  fontSize: 14,
                  color: Colors.white,
                ),
              ),
            );
          } else if (state is NotificationSettingsErrorStates) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                backgroundColor: Colors.red,
                content: CustomText(
                  text: state.error ?? "An error occurred",
                  fontSize: 14,
                  color: Colors.white,
                ),
              ),
            );
          }
        },
        builder: (context, state) {
          bool isLoading = state is NotificationSettingsLoadingStates;

          return Container(
            padding: EdgeInsets.all(20.w),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20.r),
                topRight: Radius.circular(20.r),
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Title
                Center(
                  child: CustomText(
                    text: AppStrings.notificationSettings.tr(),
                    fontSize: 18,
                    fontW: FontWeight.w600,
                    color: TColor.mainColor,
                  ),
                ),
                SizedBox(height: 5.h),

                // Divider
                Divider(color: Colors.grey.shade300, thickness: 1),
                SizedBox(height: 10.h),

                // Trip Notifications Switch
                _buildNotificationSwitch(
                  title: AppStrings.tripNotifications.tr(),
                  subtitle: AppStrings.enableTripNotifications.tr(),
                  value: _tripNotificationsEnabled,
                  isLoading: isLoading,
                  onChanged: (value) {
                    setState(() {
                      _tripNotificationsEnabled = value;
                      _saveNotificationSetting(
                          'trip_notifications_enabled', value);
                    });
                  },
                ),

                SizedBox(height: 15.h),

                // Address Change Notifications Switch
                _buildNotificationSwitch(
                  title: AppStrings.addressChangeNotifications.tr(),
                  subtitle: AppStrings.enableAddressChangeNotifications.tr(),
                  value: _addressChangeNotificationsEnabled,
                  isLoading: isLoading,
                  onChanged: (value) {
                    setState(() {
                      _addressChangeNotificationsEnabled = value;
                      _saveNotificationSetting(
                          'address_change_notifications_enabled', value);
                    });
                  },
                ),

                SizedBox(height: 15.h),

                // Absence Notifications Switch
                _buildNotificationSwitch(
                  title: AppStrings.absenceNotifications.tr(),
                  subtitle: AppStrings.enableAbsenceNotifications.tr(),
                  value: _absenceNotificationsEnabled,
                  isLoading: isLoading,
                  onChanged: (value) {
                    setState(() {
                      _absenceNotificationsEnabled = value;
                      _saveNotificationSetting(
                          'absence_notifications_enabled', value);
                    });
                  },
                ),

                SizedBox(height: 20.h),
              ],
            ),
          );
        },
      ),
    );
  }

  // Helper method to build a notification switch row
  Widget _buildNotificationSwitch({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
    bool isLoading = false,
  }) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomText(
                text: title,
                fontSize: 16,
                fontW: FontWeight.w600,
                color: Colors.black87,
              ),
              SizedBox(height: 4.h),
              CustomText(
                text: subtitle,
                fontSize: 12,
                fontW: FontWeight.w400,
                color: Colors.black54,
                maxLine: 2,
              ),
            ],
          ),
        ),
        isLoading
            ? const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                ),
              )
            : Switch(
                value: value,
                onChanged: onChanged,
                activeColor: TColor.mainColor,
              ),
      ],
    );
  }
}
